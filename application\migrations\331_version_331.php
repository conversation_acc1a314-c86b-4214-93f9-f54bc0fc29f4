<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Migration_Version_331 extends CI_Migration
{
    public function up(): void
    {
        // Create lead assignment history table
        if (!$this->db->table_exists(db_prefix() . 'lead_assignment_history')) {
            $this->db->query('CREATE TABLE `' . db_prefix() . 'lead_assignment_history` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `lead_id` int(11) NOT NULL,
                `staff_id` int(11) NOT NULL,
                `assigned_date` datetime NOT NULL,
                `assigned_by` int(11) NOT NULL DEFAULT 0,
                PRIMARY KEY (`id`),
                KEY `lead_id` (`lead_id`),
                KEY `staff_id` (`staff_id`),
                KEY `assigned_date` (`assigned_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=' . $this->db->char_set . ';');
        }

        // Populate the history table with current assignments
        $this->populate_existing_assignments();
    }

    private function populate_existing_assignments(): void
    {
        // Get all existing leads with assignments
        $leads = $this->db->query('SELECT id, assigned, addedfrom, dateadded FROM ' . db_prefix() . 'leads WHERE assigned > 0')->result();
        
        foreach ($leads as $lead) {
            // Add the creator to history if they are different from the assigned person
            if ($lead->addedfrom > 0 && $lead->addedfrom != $lead->assigned) {
                $this->db->insert(db_prefix() . 'lead_assignment_history', [
                    'lead_id' => $lead->id,
                    'staff_id' => $lead->addedfrom,
                    'assigned_date' => $lead->dateadded,
                    'assigned_by' => $lead->addedfrom
                ]);
            }
            
            // Add the currently assigned person to history
            $this->db->insert(db_prefix() . 'lead_assignment_history', [
                'lead_id' => $lead->id,
                'staff_id' => $lead->assigned,
                'assigned_date' => $lead->dateadded,
                'assigned_by' => $lead->addedfrom
            ]);
        }
    }
}
