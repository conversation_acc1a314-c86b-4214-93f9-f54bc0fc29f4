<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Lead_history_test extends Admin_controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('leads_model');
    }

    public function index()
    {
        // Only allow admin access for testing
        if (!is_admin()) {
            access_denied('Lead History Test');
        }

        echo "<h1>Lead Assignment History Test</h1>";
        
        // Test 1: Check if table exists
        echo "<h2>Test 1: Check if lead_assignment_history table exists</h2>";
        if ($this->db->table_exists(db_prefix() . 'lead_assignment_history')) {
            echo "<p style='color: green;'>✓ Table exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table does not exist - need to run migration</p>";
            $this->create_table();
        }

        // Test 2: Test assignment tracking
        echo "<h2>Test 2: Test assignment tracking functionality</h2>";
        $this->test_assignment_tracking();

        // Test 3: Test visibility queries
        echo "<h2>Test 3: Test lead visibility with assignment history</h2>";
        $this->test_lead_visibility();

        echo "<h2>Test Complete</h2>";
        echo "<p><a href='" . admin_url('leads') . "'>Back to Leads</a></p>";
    }

    private function create_table()
    {
        echo "<p>Creating lead_assignment_history table...</p>";
        
        $sql = 'CREATE TABLE `' . db_prefix() . 'lead_assignment_history` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lead_id` int(11) NOT NULL,
            `staff_id` int(11) NOT NULL,
            `assigned_date` datetime NOT NULL,
            `assigned_by` int(11) NOT NULL DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `lead_id` (`lead_id`),
            KEY `staff_id` (`staff_id`),
            KEY `assigned_date` (`assigned_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $this->db->char_set;
        
        if ($this->db->query($sql)) {
            echo "<p style='color: green;'>✓ Table created successfully</p>";
            $this->populate_existing_assignments();
        } else {
            echo "<p style='color: red;'>✗ Failed to create table</p>";
        }
    }

    private function populate_existing_assignments()
    {
        echo "<p>Populating existing assignments...</p>";
        
        // Get all existing leads with assignments
        $leads = $this->db->query('SELECT id, assigned, addedfrom, dateadded FROM ' . db_prefix() . 'leads WHERE assigned > 0')->result();
        
        $count = 0;
        foreach ($leads as $lead) {
            // Add the creator to history if they are different from the assigned person
            if ($lead->addedfrom > 0 && $lead->addedfrom != $lead->assigned) {
                $this->db->insert(db_prefix() . 'lead_assignment_history', [
                    'lead_id' => $lead->id,
                    'staff_id' => $lead->addedfrom,
                    'assigned_date' => $lead->dateadded,
                    'assigned_by' => $lead->addedfrom
                ]);
                $count++;
            }
            
            // Add the currently assigned person to history
            $this->db->insert(db_prefix() . 'lead_assignment_history', [
                'lead_id' => $lead->id,
                'staff_id' => $lead->assigned,
                'assigned_date' => $lead->dateadded,
                'assigned_by' => $lead->addedfrom
            ]);
            $count++;
        }
        
        echo "<p style='color: green;'>✓ Populated $count assignment history records</p>";
    }

    private function test_assignment_tracking()
    {
        // Test the track_lead_assignment_history method
        $test_lead_id = 1; // Use first lead for testing
        $test_staff_id = get_staff_user_id();
        
        echo "<p>Testing assignment tracking for lead ID: $test_lead_id, staff ID: $test_staff_id</p>";
        
        // Test the method exists and works
        if (method_exists($this->leads_model, 'track_lead_assignment_history')) {
            $this->leads_model->track_lead_assignment_history($test_lead_id, $test_staff_id, get_staff_user_id());
            echo "<p style='color: green;'>✓ track_lead_assignment_history method works</p>";
            
            // Check if record was created
            $history = $this->db->get_where(db_prefix() . 'lead_assignment_history', [
                'lead_id' => $test_lead_id,
                'staff_id' => $test_staff_id
            ])->row();
            
            if ($history) {
                echo "<p style='color: green;'>✓ Assignment history record created</p>";
            } else {
                echo "<p style='color: red;'>✗ Assignment history record not found</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ track_lead_assignment_history method not found</p>";
        }
    }

    private function test_lead_visibility()
    {
        $current_user_id = get_staff_user_id();
        
        // Test the new visibility query
        $new_query = '(assigned = ' . $current_user_id . ' OR addedfrom = ' . $current_user_id . ' OR is_public = 1 OR ' . db_prefix() . 'leads.id IN (SELECT lead_id FROM ' . db_prefix() . 'lead_assignment_history WHERE staff_id = ' . $current_user_id . '))';
        
        echo "<p>Testing new visibility query:</p>";
        echo "<pre>$new_query</pre>";
        
        // Count leads with new query
        $this->db->select('COUNT(*) as count');
        $this->db->from(db_prefix() . 'leads');
        $this->db->where($new_query);
        $new_count = $this->db->get()->row()->count;
        
        // Count leads with old query
        $old_query = '(assigned = ' . $current_user_id . ' OR addedfrom = ' . $current_user_id . ' OR is_public = 1)';
        $this->db->select('COUNT(*) as count');
        $this->db->from(db_prefix() . 'leads');
        $this->db->where($old_query);
        $old_count = $this->db->get()->row()->count;
        
        echo "<p>Old query result: $old_count leads</p>";
        echo "<p>New query result: $new_count leads</p>";
        
        if ($new_count >= $old_count) {
            echo "<p style='color: green;'>✓ New query returns same or more leads (includes assignment history)</p>";
        } else {
            echo "<p style='color: red;'>✗ New query returns fewer leads than old query</p>";
        }
        
        // Show some example leads that are now visible due to assignment history
        if ($new_count > $old_count) {
            echo "<h3>Leads now visible due to assignment history:</h3>";
            $this->db->select('id, name, company');
            $this->db->from(db_prefix() . 'leads');
            $this->db->where($new_query);
            $this->db->where('NOT (' . $old_query . ')');
            $this->db->limit(5);
            $additional_leads = $this->db->get()->result();
            
            if ($additional_leads) {
                echo "<ul>";
                foreach ($additional_leads as $lead) {
                    echo "<li>Lead #{$lead->id}: {$lead->name} ({$lead->company})</li>";
                }
                echo "</ul>";
            }
        }
    }
}
